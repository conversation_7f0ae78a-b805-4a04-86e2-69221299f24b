import isEqual from 'fast-deep-equal';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import React, { useEffect, useMemo, useState } from 'react';
import qs from 'query-string';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import ChatItem from '@/features/ChatItem';
import { useAgentStore } from '@/store/agent';
import { agentChatConfigSelectors, agentSelectors } from '@/store/agent/selectors';
import { useChatStore } from '@/store/chat';
import { featureFlagsSelectors, useServerConfigStore } from '@/store/serverConfig';
import { useSessionStore } from '@/store/session';
import { sessionMetaSelectors } from '@/store/session/selectors';
import { useUserStore } from '@/store/user/store';
import { userProfileSelectors } from '@/store/user/selectors';
import { processBaseVariables } from '@/utils/promptUtil';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize, { defaultSchema } from 'rehype-sanitize';
import remarkHtml from 'remark-html';
import { markdownElements } from '@/features/Conversation/components/MarkdownElements';


// 起始标签后，闭合标签前 都必须有两个换行
const normalizeMsg = (s: string | undefined) => s
  ?.replaceAll(/<([\w-]+)([^>]*)>(\n*)(\s*)/g,
    (_, t, a, nl, sp) => `<${t}${a}>${nl.length >= 2 ? nl : '\n\n'}${sp}`)
  ?.replaceAll(/(\n*)(\s*)(<\/[\w-]+>)/g,
    (_, nl, sp, t) => `${nl.length >= 2 ? nl : '\n\n'}${sp}${t}`);


// import OpeningQuestions from './OpeningQuestions';

const WelcomeMessage = () => {
  const mobile = useServerConfigStore((s) => s.isMobile);
  const { t } = useTranslation('chat');
  const type = useAgentStore(agentChatConfigSelectors.displayMode);
  // const openingMessage = useAgentStore(agentSelectors.openingMessage);
  // const openingQuestions = useAgentStore(agentSelectors.openingQuestions);

  const meta = useSessionStore(sessionMetaSelectors.currentAgentMeta, isEqual);
  const { isAgentEditable } = useServerConfigStore(featureFlagsSelectors);
  const activeId = useChatStore((s) => s.activeId);

  const agentSystemRoleMsg = t('agentDefaultMessageWithoutEdit', {
    name: meta.title || t('defaultAgent'),
    systemRole: meta.description,
  });

  const agentMsg = t(isAgentEditable ? 'agentDefaultMessage' : 'agentDefaultMessageWithoutEdit', {
    name: meta.title || t('defaultAgent'),
    url: qs.stringifyUrl({
      query: mobile ? { session: activeId, showMobileWorkspace: mobile } : { session: activeId },
      url: '/chat/settings',
    }),
  });

  // 原代码
  // const message = useMemo(() => {
  //   if (openingMessage) return openingMessage;
  //   return !!meta.description ? agentSystemRoleMsg : agentMsg;
  // }, [openingMessage, agentSystemRoleMsg, agentMsg, meta.description]);
  //
  // const chatItem = (
  //   <ChatItem
  //     avatar={meta}
  //     editing={false}
  //     message={message}
  //     placement={'left'}
  //     variant={type === 'chat' ? 'bubble' : 'docs'}
  //   />
  // );

  // return openingQuestions.length > 0 ? (
  //   <Flexbox>
  //     {chatItem}
  //     <OpeningQuestions mobile={mobile} questions={openingQuestions} />
  //   </Flexbox>
  // ) : (
  //   chatItem
  // );

  const nickname = useUserStore((s) => userProfileSelectors.userProfile(s)?.fullName);

  // ai开场白
  const roleFirstMsgs = useAgentStore((s) => {
    const config = agentSelectors.currentAgentConfig(s);
    return (config.roleFirstMsgs || []).map(item => processBaseVariables(item, config.title || '', nickname));
  });

  const [currentIndex, setCurrentIndex] = useState(0);

  // 当roleFirstMsgs变化时重置索引
  useEffect(() => {
    setCurrentIndex(0);
  }, [roleFirstMsgs]);

  // 修改切换处理函数
  const handlePrev = () => {
    setCurrentIndex(prev => (prev - 1 + roleFirstMsgs.length) % roleFirstMsgs.length);
  };

  const handleNext = () => {
    setCurrentIndex(prev => (prev + 1) % roleFirstMsgs.length);
  };

  let msg = agentMsg;
  if (roleFirstMsgs.length > 0) {
    msg = roleFirstMsgs[currentIndex];
  } else if (!!meta.description) {
    msg = agentSystemRoleMsg;
  }

  const hasMultipleMessages = roleFirstMsgs.length > 1;

  // 添加store方法
  const { setRoleFirstMsg } = useChatStore();

  // 在msg变化时更新store
  useEffect(() => {
    if (roleFirstMsgs.length > 0 && msg) {
      setRoleFirstMsg(msg);
    }
    return () => {
      if (roleFirstMsgs.length > 0) {
        setRoleFirstMsg(undefined);
      }
    };
  }, [msg, roleFirstMsgs.length]);


  // html 和 markdown
  const rehypePlugins = markdownElements.map((element) => element.rehypePlugin).filter(Boolean);
  const remarkPlugins = markdownElements.map((element) => element.remarkPlugin).filter(Boolean);
  const customSchema = {
    ...defaultSchema,
    attributes: {
      ...defaultSchema.attributes,
      '*': (defaultSchema.attributes?.['*'] || []).filter(attr =>
        (typeof attr === 'string' ? attr : attr[0]) !== 'className' &&
        !(typeof attr === 'string' ? attr : attr[0]).startsWith('on')
      )
    }
  };
  const markdownProps = useMemo(
    () => ({
      // components,
      // customRender: markdownCustomRender,
      enableCustomFootnotes: true,
      rehypePlugins: [
        ...rehypePlugins,
        rehypeRaw,
        [rehypeSanitize, customSchema]
      ],
      remarkPlugins: [
        ...remarkPlugins,
        remarkHtml
      ]
    }),
    [rehypePlugins, remarkPlugins]
  );

  return (
    <Flexbox gap={8}>
      <Flexbox gap={8} style={{ position: 'relative', width: 'fit-content' }}>
        <ChatItem
          avatar={meta}
          editing={false}
          markdownProps={markdownProps}
          message={normalizeMsg(msg)}
          placement={'left'}
          variant={type === 'chat' ? 'bubble' : 'docs'}
        />
        {hasMultipleMessages && (
          <Flexbox
            align="center"
            gap={4}
            horizontal
            style={{
              borderRadius: 4,
              bottom: -20,
              left: 70,
              padding: '2px 4px 8px 0',
              position: 'absolute',
            }}
          >
            <ChevronLeft
              onMouseDown={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handlePrev();
              }}
              size={16}
              style={{
                color: '#666',
                cursor: 'pointer',
                strokeWidth: 2.5,
              }}
            />
            <span
              style={{
                color: '#333',
                fontSize: 12,
                fontWeight: 500,
                minWidth: 60,
                textAlign: 'center',
              }}
            >
              {currentIndex + 1} / {roleFirstMsgs.length}
            </span>
            <ChevronRight
              onMouseDown={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleNext();
              }}
              size={16}
              style={{
                color: '#666',
                cursor: 'pointer',
                strokeWidth: 2.5,
              }}
            />
          </Flexbox>
        )}
      </Flexbox>
    </Flexbox>
  );
};
export default WelcomeMessage;
