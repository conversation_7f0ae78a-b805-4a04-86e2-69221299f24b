'use client';

import { Form } from '@lobehub/ui';
import { createStyles } from 'antd-style';
import { memo } from 'react';
// import { useTranslation } from 'react-i18next';

import { FORM_STYLE } from '@/const/layoutTokens';

const useStyles = createStyles(({ css }) => ({
  placeholder: css`
    color: #666;
    font-size: 14px;
    padding: 20px 0;
    text-align: center;
  `,
}));

const WorldBook = memo(() => {
  // const { t } = useTranslation('setting');
  const { styles } = useStyles();

  return (
    <Form
      items={[
        {
          children: (
            <div className={styles.placeholder}>
              开发对接中...敬请期待
            </div>
          ),
          title: '世界书',
        },
      ]}
      itemsType={'group'}
      variant={'filled'}
      {...FORM_STYLE}
    />
  );
});

export default WorldBook;
